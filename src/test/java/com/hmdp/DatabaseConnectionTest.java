package com.hmdp;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=************************************************************************************************************************************",
    "spring.datasource.username=root",
    "spring.datasource.password=123321",
    "spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver"
})
public class DatabaseConnectionTest {

    @Test
    public void testDirectJdbcConnection() {
        String url = "************************************************************************************************************************************";
        String username = "root";
        String password = "123321";
        
        try {
            System.out.println("Testing direct JDBC connection...");
            
            // Load the driver
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // Create connection
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ Connection successful!");
            
            // Test charset settings
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SHOW VARIABLES LIKE 'character_set%'");
            
            System.out.println("\n📊 Database charset settings:");
            while (rs.next()) {
                System.out.println(rs.getString("Variable_name") + ": " + rs.getString("Value"));
            }
            
            // Test Chinese character retrieval
            rs = stmt.executeQuery("SELECT name, area FROM tb_shop WHERE name LIKE '%店%' OR name LIKE '%餐%' LIMIT 3");
            System.out.println("\n🏪 Chinese shop names:");
            while (rs.next()) {
                System.out.println("Name: " + rs.getString("name") + ", Area: " + rs.getString("area"));
            }
            
            // Test Chinese character insertion
            stmt.executeUpdate("INSERT INTO tb_user (phone, password, nick_name, icon) VALUES ('13888888888', '', '测试连接', '/test.jpg')");
            System.out.println("✅ Chinese character insertion successful!");
            
            // Verify the insertion
            rs = stmt.executeQuery("SELECT nick_name FROM tb_user WHERE phone = '13888888888'");
            if (rs.next()) {
                String nickName = rs.getString("nick_name");
                System.out.println("✅ Retrieved Chinese name: " + nickName);
                System.out.println("✅ Matches expected: " + "测试连接".equals(nickName));
            }
            
            // Clean up
            stmt.executeUpdate("DELETE FROM tb_user WHERE phone = '13888888888'");
            
            connection.close();
            System.out.println("✅ Test completed successfully!");
            
        } catch (Exception e) {
            System.err.println("❌ Connection failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
