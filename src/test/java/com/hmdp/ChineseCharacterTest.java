package com.hmdp;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

@SpringBootTest
public class ChineseCharacterTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Test
    public void testChineseCharacterInsertion() {
        // Test inserting Chinese characters
        String insertSql = "INSERT INTO tb_user (phone, password, nick_name, icon) VALUES (?, ?, ?, ?)";
        jdbcTemplate.update(insertSql, "13999999999", "", "测试用户", "/test.jpg");
        
        // Test retrieving Chinese characters
        String selectSql = "SELECT phone, nick_name FROM tb_user WHERE phone = ?";
        List<Map<String, Object>> results = jdbcTemplate.queryForList(selectSql, "13999999999");
        
        if (!results.isEmpty()) {
            Map<String, Object> user = results.get(0);
            String nickName = (String) user.get("nick_name");
            System.out.println("Retrieved nick_name: " + nickName);
            System.out.println("Expected: 测试用户");
            System.out.println("Match: " + "测试用户".equals(nickName));
        }
        
        // Clean up
        jdbcTemplate.update("DELETE FROM tb_user WHERE phone = ?", "13999999999");
    }

    @Test
    public void testExistingChineseData() {
        // Test retrieving existing Chinese data
        String sql = "SELECT id, name, area FROM tb_shop WHERE name LIKE '%烤鸭%' OR name LIKE '%小笼包%' LIMIT 3";
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        
        System.out.println("Found " + results.size() + " shops with Chinese names:");
        for (Map<String, Object> shop : results) {
            System.out.println("ID: " + shop.get("id") + 
                             ", Name: " + shop.get("name") + 
                             ", Area: " + shop.get("area"));
        }
    }

    @Test
    public void testDatabaseCharset() {
        // Check database charset settings
        String sql = "SHOW VARIABLES LIKE 'character_set%'";
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
        
        System.out.println("Database charset settings:");
        for (Map<String, Object> row : results) {
            System.out.println(row.get("Variable_name") + ": " + row.get("Value"));
        }
    }
}
