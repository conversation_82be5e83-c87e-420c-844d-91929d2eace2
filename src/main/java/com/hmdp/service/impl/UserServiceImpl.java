package com.hmdp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hmdp.dto.LoginFormDTO;
import com.hmdp.dto.Result;
import com.hmdp.entity.User;
import com.hmdp.mapper.UserMapper;
import com.hmdp.service.IUserService;
import com.hmdp.utils.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import java.io.StringReader;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.hmdp.utils.RedisConstants.LOGIN_CODE_KEY;
import static com.hmdp.utils.SystemConstants.USER_NICK_NAME_PREFIX;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public Result sendCode(String phone, HttpSession session) {
        String randomNumbers = RandomUtil.randomNumbers(6);
        session.setAttribute("code", randomNumbers);
        log.debug("发送验证码：{}", randomNumbers);

        stringRedisTemplate.opsForValue().set(LOGIN_CODE_KEY+ phone, randomNumbers, RedisConstants.LOGIN_CODE_TTL, TimeUnit.MINUTES);

        return Result.ok();
    }

    @Override
    public Result login(LoginFormDTO loginForm, HttpSession session, HttpServletResponse response) {
        String code = loginForm.getCode();
        String sessionCode = (String) session.getAttribute("code");
//        if (sessionCode == null || !code.equals(sessionCode)) {
//            return Result.fail("验证码错误");
//        }
        String phone = loginForm.getPhone();
        User user = query().eq("phone", phone).one();

        if (user == null) {
            user = createUserWithPhone(phone);
        }

        String redisCode = stringRedisTemplate.opsForValue().get(LOGIN_CODE_KEY + phone);

//        if (redisCode == null || !redisCode.equals(code)) {
//            return Result.fail("验证码错误");
//        }


        //convert user to userDTO
        com.hmdp.dto.UserDTO userDTO = BeanUtil.copyProperties(user, com.hmdp.dto.UserDTO.class);
        session.setAttribute("user", userDTO);

        //generate token by UUID for user and  save userDTO to redis with hash
        String token = UUID.randomUUID().toString().replace("-", "");

        //covert userDTO to map
        Map<String, Object> userMap = BeanUtil.beanToMap(userDTO, new HashMap<>(), CopyOptions.create().setIgnoreNullValue(true).setFieldValueEditor((fieldName, fieldValue) -> fieldValue.toString()));
        stringRedisTemplate.opsForHash().putAll(RedisConstants.LOGIN_USER_KEY + token, userMap);

        stringRedisTemplate.expire(RedisConstants.LOGIN_USER_KEY + token, RedisConstants.LOGIN_USER_TTL, TimeUnit.SECONDS);

//        //create response and write token to response header with authorization
//        response.setHeader("authorization", token);


        return Result.ok(token);
    }

//    @Override
//    public Result login(LoginFormDTO loginForm, HttpSession session) {
//        String code = loginForm.getCode();
//        String sessionCode = (String) session.getAttribute("code");
////        if (sessionCode == null || !code.equals(sessionCode)) {
////            return Result.fail("验证码错误");
////        }
//        String phone = loginForm.getPhone();
//        User user = query().eq("phone", phone).one();
//        if (user == null) {
//            user = createUserWithPhone(phone);
//        }
//
//
//        //convert user to userDTO
//        com.hmdp.dto.UserDTO userDTO = BeanUtil.copyProperties(user, com.hmdp.dto.UserDTO.class);
//        session.setAttribute("user", userDTO);
//
//
//
//
//        return Result.ok(user);
//    }

    private User createUserWithPhone(String phone) {
        User user;
        user = new User();
        user.setPhone(phone);
        user.setNickName(USER_NICK_NAME_PREFIX + RandomUtil.randomString(10));
        save(user);
        return user;
    }


}
