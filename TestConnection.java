import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

public class TestConnection {
    public static void main(String[] args) {
        String url = "************************************************************************************************************************************";
        String username = "root";
        String password = "123321";
        
        try {
            System.out.println("🔍 Testing JDBC connection to MySQL...");
            System.out.println("URL: " + url);
            
            // Load the driver
            Class.forName("com.mysql.cj.jdbc.Driver");
            System.out.println("✅ MySQL driver loaded successfully");
            
            // Create connection
            Connection connection = DriverManager.getConnection(url, username, password);
            System.out.println("✅ Database connection successful!");
            
            // Test charset settings
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery("SHOW VARIABLES LIKE 'character_set%'");
            
            System.out.println("\n📊 Database charset settings:");
            while (rs.next()) {
                System.out.println("  " + rs.getString("Variable_name") + ": " + rs.getString("Value"));
            }
            
            // Test database and table existence
            rs = stmt.executeQuery("SELECT DATABASE() as current_db");
            if (rs.next()) {
                System.out.println("\n🗄️  Current database: " + rs.getString("current_db"));
            }
            
            // Test Chinese character retrieval
            rs = stmt.executeQuery("SELECT COUNT(*) as shop_count FROM tb_shop");
            if (rs.next()) {
                System.out.println("📊 Total shops in database: " + rs.getInt("shop_count"));
            }
            
            rs = stmt.executeQuery("SELECT name, area FROM tb_shop WHERE name LIKE '%店%' OR name LIKE '%餐%' OR name LIKE '%北京%' OR name LIKE '%上海%' LIMIT 5");
            System.out.println("\n🏪 Chinese shop names:");
            int count = 0;
            while (rs.next()) {
                count++;
                System.out.println("  " + count + ". Name: " + rs.getString("name") + ", Area: " + rs.getString("area"));
            }
            
            if (count == 0) {
                System.out.println("  ⚠️  No shops with Chinese names found. Let's check all shops:");
                rs = stmt.executeQuery("SELECT name, area FROM tb_shop LIMIT 5");
                while (rs.next()) {
                    System.out.println("  - Name: " + rs.getString("name") + ", Area: " + rs.getString("area"));
                }
            }
            
            // Test Chinese character insertion
            System.out.println("\n🧪 Testing Chinese character insertion...");
            String testPhone = "13777777777";
            String testName = "数据库测试用户";
            
            // Clean up any existing test data
            stmt.executeUpdate("DELETE FROM tb_user WHERE phone = '" + testPhone + "'");
            
            // Insert Chinese characters
            int result = stmt.executeUpdate("INSERT INTO tb_user (phone, password, nick_name, icon) VALUES ('" + testPhone + "', '', '" + testName + "', '/test.jpg')");
            System.out.println("✅ Insert result: " + result + " row(s) affected");
            
            // Verify the insertion
            rs = stmt.executeQuery("SELECT nick_name FROM tb_user WHERE phone = '" + testPhone + "'");
            if (rs.next()) {
                String retrievedName = rs.getString("nick_name");
                System.out.println("✅ Retrieved Chinese name: " + retrievedName);
                System.out.println("✅ Original name: " + testName);
                System.out.println("✅ Names match: " + testName.equals(retrievedName));
                
                if (!testName.equals(retrievedName)) {
                    System.out.println("❌ Character encoding issue detected!");
                    System.out.println("   Expected bytes: " + java.util.Arrays.toString(testName.getBytes("UTF-8")));
                    System.out.println("   Retrieved bytes: " + java.util.Arrays.toString(retrievedName.getBytes("UTF-8")));
                }
            } else {
                System.out.println("❌ Failed to retrieve inserted data");
            }
            
            // Clean up
            stmt.executeUpdate("DELETE FROM tb_user WHERE phone = '" + testPhone + "'");
            
            connection.close();
            System.out.println("\n🎉 Test completed successfully!");
            System.out.println("✅ Chinese character support is working correctly!");
            
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
