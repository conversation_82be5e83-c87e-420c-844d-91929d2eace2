version: '3.8'

services:
  mysql:
    image: mysql:5.7
    container_name: hm-dianping-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123321
      MYSQL_DATABASE: hmdp
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/db/hmdp.sql:/docker-entrypoint-initdb.d/hmdp.sql:ro
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - hmdp-network

  redis:
    image: redis:6.2-alpine
    container_name: hm-dianping-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - hmdp-network

volumes:
  mysql_data:
  redis_data:

networks:
  hmdp-network:
    driver: bridge
