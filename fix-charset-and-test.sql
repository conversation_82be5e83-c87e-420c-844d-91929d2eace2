-- Comprehensive fix for Chinese character support
USE `hmdp`;

-- Set proper encoding for this session
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_results = utf8mb4;

-- Clean up test data that was inserted with wrong charset
DELETE FROM tb_user WHERE phone IN ('13900000001', '13900000002', '13900000003');
DELETE FROM tb_shop WHERE name IN ('北京烤鸭店', '上海小笼包', '广州茶餐厅');
DELETE FROM tb_shop_type WHERE name IN ('中式餐厅', '火锅店', '茶餐厅');

-- Ensure all tables have proper charset (just to be safe)
ALTER TABLE tb_user CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tb_shop CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tb_shop_type CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tb_blog CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE tb_user_info CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Now test Chinese character insertion with proper charset
INSERT INTO `tb_shop` (`name`, `type_id`, `images`, `area`, `address`, `x`, `y`, `avg_price`, `sold`, `comments`, `score`, `open_hours`) VALUES 
('北京烤鸭店', 1, 'test.jpg', '朝阳区', '北京市朝阳区建国门外大街1号', 116.397128, 39.916527, 120, 1000, 500, 45, '10:00-22:00'),
('上海小笼包', 1, 'test.jpg', '黄浦区', '上海市黄浦区南京东路100号', 121.473701, 31.230416, 80, 800, 300, 42, '09:00-21:00'),
('广州茶餐厅', 1, 'test.jpg', '天河区', '广州市天河区天河路123号', 113.280637, 23.125178, 90, 1200, 600, 40, '08:00-23:00');

INSERT INTO `tb_shop_type` (`name`, `icon`, `sort`) VALUES 
('中式餐厅', '/types/chinese.png', 100),
('火锅店', '/types/hotpot.png', 101),
('茶餐厅', '/types/teahouse.png', 102);

INSERT INTO `tb_user` (`phone`, `password`, `nick_name`, `icon`) VALUES 
('13900000001', '', '张三', '/imgs/icons/user1.jpg'),
('13900000002', '', '李四', '/imgs/icons/user2.jpg'),
('13900000003', '', '王五', '/imgs/icons/user3.jpg');

-- Test blog content with Chinese
INSERT INTO `tb_blog` (`shop_id`, `user_id`, `title`, `images`, `content`, `liked`, `comments`) VALUES 
((SELECT id FROM tb_shop WHERE name = '北京烤鸭店' LIMIT 1), 
 (SELECT id FROM tb_user WHERE nick_name = '张三' LIMIT 1), 
 '北京烤鸭店探店记', 
 'test1.jpg,test2.jpg', 
 '今天去了一家很棒的北京烤鸭店，味道非常正宗，皮脆肉嫩，配上甜面酱和黄瓜丝，简直是人间美味！强烈推荐大家去尝试。', 
 50, 10);

-- Verify the results
SELECT '=== 中文测试结果 ===' as test_result;

SELECT '商店信息:' as label;
SELECT id, name, area, address FROM tb_shop WHERE name IN ('北京烤鸭店', '上海小笼包', '广州茶餐厅');

SELECT '商店类型:' as label;
SELECT id, name FROM tb_shop_type WHERE name IN ('中式餐厅', '火锅店', '茶餐厅');

SELECT '用户信息:' as label;
SELECT id, phone, nick_name FROM tb_user WHERE nick_name IN ('张三', '李四', '王五');

SELECT '博客内容:' as label;
SELECT id, title, LEFT(content, 50) as content_preview FROM tb_blog WHERE title = '北京烤鸭店探店记';

-- Check charset settings
SELECT '=== 字符集设置 ===' as charset_info;
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';
