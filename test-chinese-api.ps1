# PowerShell script to test Chinese character support in the API
# Make sure the application is running on localhost:8081

$baseUrl = "http://localhost:8081"

Write-Host "Testing Chinese Character Support in HMDP API" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Test 1: Get shop types (should include Chinese names)
Write-Host "`n1. Testing Shop Types API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop-type/list" -Method GET -ContentType "application/json; charset=utf-8"
    Write-Host "Shop Types Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3 | Write-Host
} catch {
    Write-Host "Error testing shop types: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Get shops (should include Chinese names)
Write-Host "`n2. Testing Shops API..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/shop/of/type?typeId=1&current=1&x=121.445&y=31.213" -Method GET -ContentType "application/json; charset=utf-8"
    Write-Host "Shops Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3 | Write-Host
} catch {
    Write-Host "Error testing shops: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Test user registration with Chinese nickname
Write-Host "`n3. Testing User Registration with Chinese Name..." -ForegroundColor Yellow
try {
    # First get a verification code
    $codeResponse = Invoke-RestMethod -Uri "$baseUrl/user/code?phone=13999888777" -Method POST -ContentType "application/json; charset=utf-8"
    Write-Host "Code Response: $($codeResponse | ConvertTo-Json)" -ForegroundColor Cyan
    
    # Then try to login/register (this will create a user)
    $loginData = @{
        phone = "13999888777"
        code = "123456"  # Default code for testing
    } | ConvertTo-Json -Compress
    
    $loginResponse = Invoke-RestMethod -Uri "$baseUrl/user/login" -Method POST -Body $loginData -ContentType "application/json; charset=utf-8"
    Write-Host "Login Response: $($loginResponse | ConvertTo-Json)" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error testing user registration: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Direct database query test
Write-Host "`n4. Testing Direct Database Connection..." -ForegroundColor Yellow
try {
    # This requires the application to be running
    Write-Host "Please check the application logs for Chinese character display" -ForegroundColor Cyan
    Write-Host "You can also check the database directly using:" -ForegroundColor Cyan
    Write-Host "docker exec -it hm-dianping-mysql mysql -uroot -p123321 -e `"USE hmdp; SELECT name FROM tb_shop WHERE name LIKE '%中%' OR name LIKE '%店%' LIMIT 5;`"" -ForegroundColor Gray
} catch {
    Write-Host "Error in database test: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=============================================" -ForegroundColor Green
Write-Host "Test completed. Check the responses above for Chinese character display." -ForegroundColor Green
Write-Host "If Chinese characters appear correctly, the fix is working!" -ForegroundColor Green
