# PowerShell script to test database connection and Chinese character support
Write-Host "🔍 Testing Database Connection and Chinese Character Support" -ForegroundColor Green
Write-Host "==========================================================" -ForegroundColor Green

# Test 1: Check if MySQL port is accessible
Write-Host "`n1. Testing MySQL port connectivity..." -ForegroundColor Yellow
try {
    $connection = Test-NetConnection -ComputerName localhost -Port 3307 -WarningAction SilentlyContinue
    if ($connection.TcpTestSucceeded) {
        Write-Host "✅ MySQL port 3307 is accessible" -ForegroundColor Green
    } else {
        Write-Host "❌ MySQL port 3307 is not accessible" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error testing port: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Test database connection using Docker exec
Write-Host "`n2. Testing database connection..." -ForegroundColor Yellow
try {
    $result = docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "SELECT 'Connection successful!' as status;" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database connection successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Database connection failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error connecting to database: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 3: Check database charset settings
Write-Host "`n3. Checking database charset settings..." -ForegroundColor Yellow
try {
    $charsetResult = docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "USE hmdp; SHOW VARIABLES LIKE 'character_set_database';" 2>$null
    Write-Host "Database charset configuration:" -ForegroundColor Cyan
    Write-Host $charsetResult -ForegroundColor White
} catch {
    Write-Host "❌ Error checking charset: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Test Chinese character data
Write-Host "`n4. Testing Chinese character data..." -ForegroundColor Yellow
try {
    # Insert a test record with Chinese characters
    $insertSql = "USE hmdp; SET NAMES utf8mb4; INSERT INTO tb_user (phone, password, nick_name, icon) VALUES ('13666666666', '', '测试用户', '/test.jpg') ON DUPLICATE KEY UPDATE nick_name='测试用户';"
    docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "$insertSql" 2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Chinese character insertion successful" -ForegroundColor Green
        
        # Retrieve the data to verify
        $selectSql = "USE hmdp; SET NAMES utf8mb4; SELECT phone, nick_name FROM tb_user WHERE phone = '13666666666';"
        $retrieveResult = docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "$selectSql" 2>$null
        Write-Host "Retrieved data:" -ForegroundColor Cyan
        Write-Host $retrieveResult -ForegroundColor White
        
        # Clean up
        $deleteSql = "USE hmdp; DELETE FROM tb_user WHERE phone = '13666666666';"
        docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "$deleteSql" 2>$null
        
    } else {
        Write-Host "❌ Chinese character insertion failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Error testing Chinese characters: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Check existing Chinese data
Write-Host "`n5. Checking existing Chinese data..." -ForegroundColor Yellow
try {
    $existingDataSql = "USE hmdp; SET NAMES utf8mb4; SELECT name, area FROM tb_shop WHERE name REGEXP '[\\u4e00-\\u9fff]' LIMIT 3;"
    $existingResult = docker exec -i hm-dianping-mysql mysql -uroot -p123321 -e "$existingDataSql" 2>$null
    Write-Host "Existing Chinese shop data:" -ForegroundColor Cyan
    Write-Host $existingResult -ForegroundColor White
} catch {
    Write-Host "❌ Error checking existing data: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Verify application configuration
Write-Host "`n6. Verifying application configuration..." -ForegroundColor Yellow
$configFile = "src/main/resources/application.yaml"
if (Test-Path $configFile) {
    $config = Get-Content $configFile -Raw
    if ($config -match "localhost:3307") {
        Write-Host "✅ Application configured to use localhost:3307" -ForegroundColor Green
    } else {
        Write-Host "❌ Application not configured for localhost:3307" -ForegroundColor Red
    }
    
    if ($config -match "characterEncoding=utf8") {
        Write-Host "✅ Application configured with UTF-8 encoding" -ForegroundColor Green
    } else {
        Write-Host "❌ Application missing UTF-8 encoding configuration" -ForegroundColor Red
    }
    
    if ($config -match "com.mysql.cj.jdbc.Driver") {
        Write-Host "✅ Application using MySQL 8.x driver" -ForegroundColor Green
    } else {
        Write-Host "❌ Application not using MySQL 8.x driver" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Application configuration file not found" -ForegroundColor Red
}

Write-Host "`n==========================================================" -ForegroundColor Green
Write-Host "🎉 Database connection and Chinese character test completed!" -ForegroundColor Green
Write-Host "✅ Your application should now be able to connect to the database" -ForegroundColor Green
Write-Host "✅ Chinese characters should work correctly" -ForegroundColor Green
Write-Host "`n💡 Next steps:" -ForegroundColor Yellow
Write-Host "   1. Start your Spring Boot application" -ForegroundColor White
Write-Host "   2. The application should connect successfully to MySQL" -ForegroundColor White
Write-Host "   3. Chinese characters should display and save correctly" -ForegroundColor White
