-- <PERSON>ript to recreate HMDP database with proper UTF-8 support
-- Run this script to fix Chinese character insertion issues

-- Drop existing database if it exists
DROP DATABASE IF EXISTS `hmdp`;

-- Create database with proper UTF8MB4 charset
CREATE DATABASE `hmdp` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `hmdp`;

-- Set session variables for proper UTF-8 handling
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_database = utf8mb4;
SET character_set_results = utf8mb4;
SET character_set_server = utf8mb4;

-- Verify charset settings
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- Now run the main schema file
SOURCE /docker-entrypoint-initdb.d/hmdp.sql;
