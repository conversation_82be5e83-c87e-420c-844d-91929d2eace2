-- Simple test for Chinese character insertion
USE `hmdp`;

-- Set proper encoding
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Test 1: Insert Chinese shop names
INSERT INTO `tb_shop` (`name`, `type_id`, `images`, `area`, `address`, `x`, `y`, `avg_price`, `sold`, `comments`, `score`, `open_hours`) VALUES 
('北京烤鸭店', 1, 'test.jpg', '朝阳区', '北京市朝阳区建国门外大街1号', 116.397128, 39.916527, 120, 1000, 500, 45, '10:00-22:00');

-- Test 2: Insert Chinese shop type
INSERT INTO `tb_shop_type` (`name`, `icon`, `sort`) VALUES 
('中式餐厅', '/types/chinese.png', 100);

-- Test 3: Insert Chinese user name
INSERT INTO `tb_user` (`phone`, `password`, `nick_name`, `icon`) VALUES 
('13900000001', '', '张三', '/imgs/icons/user1.jpg');

-- Verify the insertions
SELECT '=== 测试结果 ===' as result;
SELECT id, name, area, address FROM tb_shop WHERE name = '北京烤鸭店';
SELECT id, name FROM tb_shop_type WHERE name = '中式餐厅';
SELECT id, phone, nick_name FROM tb_user WHERE nick_name = '张三';

-- Check character set
SELECT '=== 字符集检查 ===' as charset_check;
SHOW VARIABLES LIKE 'character_set_database';
SHOW VARIABLES LIKE 'collation_database';
