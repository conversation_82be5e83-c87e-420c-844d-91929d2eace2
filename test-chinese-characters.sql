-- Test script for Chinese character insertion
-- Use this to verify that Chinese characters work properly

USE `hmdp`;

-- Test Chinese characters in shop names
INSERT INTO `tb_shop` (`name`, `type_id`, `images`, `area`, `address`, `x`, `y`, `avg_price`, `sold`, `comments`, `score`, `open_hours`, `create_time`, `update_time`) VALUES
('北京烤鸭店', 1, 'test.jpg', '朝阳区', '北京市朝阳区建国门外大街1号', 116.397128, 39.916527, 120, 0000001000, 0000000500, 45, '10:00-22:00', NOW(), NOW()),
('上海小笼包', 1, 'test.jpg', '黄浦区', '上海市黄浦区南京东路100号', 121.473701, 31.230416, 80, 0000000800, 0000000300, 42, '09:00-21:00', NOW(), NOW()),
('广州茶餐厅', 1, 'test.jpg', '天河区', '广州市天河区天河路123号', 113.280637, 23.125178, 90, 0000001200, 0000000600, 40, '08:00-23:00', NOW(), NOW());

-- Test Chinese characters in shop types
INSERT INTO `tb_shop_type` (`name`, `icon`, `sort`, `create_time`, `update_time`) VALUES
('中式餐厅', '/types/chinese.png', 100, NOW(), NOW()),
('火锅店', '/types/hotpot.png', 101, NOW(), NOW()),
('茶餐厅', '/types/teahouse.png', 102, NOW(), NOW());

-- Test Chinese characters in user names
INSERT INTO `tb_user` (`phone`, `password`, `nick_name`, `icon`, `create_time`, `update_time`) VALUES
('13900000001', '', '张三', '/imgs/icons/user1.jpg', NOW(), NOW()),
('13900000002', '', '李四', '/imgs/icons/user2.jpg', NOW(), NOW()),
('13900000003', '', '王五', '/imgs/icons/user3.jpg', NOW(), NOW());

-- Get the last inserted user IDs for reference
SET @user1 = (SELECT id FROM tb_user WHERE phone = '13900000001');
SET @user2 = (SELECT id FROM tb_user WHERE phone = '13900000002');
SET @user3 = (SELECT id FROM tb_user WHERE phone = '13900000003');

-- Get the last inserted shop IDs for reference
SET @shop1 = (SELECT id FROM tb_shop WHERE name = '北京烤鸭店');
SET @shop2 = (SELECT id FROM tb_shop WHERE name = '上海小笼包');
SET @shop3 = (SELECT id FROM tb_shop WHERE name = '广州茶餐厅');

-- Test Chinese characters in user info
INSERT INTO `tb_user_info` (`user_id`, `city`, `introduce`, `fans`, `followee`, `gender`, `birthday`, `credits`) VALUES
(@user1, '北京', '我是一个热爱美食的北京人，喜欢尝试各种新鲜的菜品。', 10, 5, 0, '1990-01-01', 100),
(@user2, '上海', '上海本地人，对小笼包情有独钟。', 15, 8, 1, '1992-05-15', 150),
(@user3, '广州', '广州吃货一枚，最爱粤菜和早茶。', 20, 12, 0, '1988-10-20', 200);

-- Test Chinese characters in blog content
INSERT INTO `tb_blog` (`shop_id`, `user_id`, `title`, `images`, `content`, `liked`, `comments`, `create_time`, `update_time`) VALUES
(@shop1, @user1, '北京烤鸭店探店记', 'test1.jpg,test2.jpg', '今天去了一家很棒的北京烤鸭店，味道非常正宗，皮脆肉嫩，配上甜面酱和黄瓜丝，简直是人间美味！强烈推荐大家去尝试。', 50, 10, NOW(), NOW()),
(@shop2, @user2, '上海小笼包测评', 'test3.jpg', '这家小笼包店的汤汁很足，皮薄馅大，一口咬下去汤汁四溢，非常满足。价格也很实惠，性价比很高。', 30, 5, NOW(), NOW()),
(@shop3, @user3, '广州早茶体验', 'test4.jpg,test5.jpg', '广州的早茶文化真的很棒，虾饺、烧卖、叉烧包，每一样都做得很精致。和朋友一起喝茶聊天，享受慢生活的节奏。', 40, 8, NOW(), NOW());

-- Verify the data was inserted correctly
SELECT '=== 商店信息 ===' as info;
SELECT id, name, area, address FROM tb_shop WHERE name IN ('北京烤鸭店', '上海小笼包', '广州茶餐厅');

SELECT '=== 商店类型 ===' as info;
SELECT id, name FROM tb_shop_type WHERE name IN ('中式餐厅', '火锅店', '茶餐厅');

SELECT '=== 用户信息 ===' as info;
SELECT id, phone, nick_name FROM tb_user WHERE nick_name IN ('张三', '李四', '王五');

SELECT '=== 用户详情 ===' as info;
SELECT user_id, city, introduce FROM tb_user_info WHERE city IN ('北京', '上海', '广州');

SELECT '=== 博客内容 ===' as info;
SELECT id, title, content FROM tb_blog WHERE title LIKE '%探店%' OR title LIKE '%测评%' OR title LIKE '%体验%';

-- Check character set settings
SELECT '=== 字符集设置 ===' as info;
SHOW VARIABLES LIKE 'character_set%';
