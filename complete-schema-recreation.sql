-- Complete schema recreation script with proper UTF-8 support
-- This will delete everything and recreate with correct charset

-- Drop the entire database
DROP DATABASE IF EXISTS `hmdp`;

-- Create database with proper UTF8MB4 charset
CREATE DATABASE `hmdp` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- Use the database
USE `hmdp`;

-- Set proper encoding for this session
SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;
SET character_set_client = utf8mb4;
SET character_set_connection = utf8mb4;
SET character_set_database = utf8mb4;
SET character_set_results = utf8mb4;
SET character_set_server = utf8mb4;

-- Disable foreign key checks during recreation
SET FOREIGN_KEY_CHECKS = 0;

-- Show charset settings for verification
SELECT 'Database charset settings:' as info;
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

SELECT 'Schema recreation completed. Ready to run main schema file.' as status;
